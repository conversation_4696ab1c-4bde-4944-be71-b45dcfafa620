import Image from "next/image";
import Link from "next/link";
import React from "react";
import {
  Pi<PERSON>allet<PERSON>ight,
  PiBellLight,
  Pi<PERSON>serCircleLight,
  PiGearLight,
  PiListLight,
} from "react-icons/pi";

const Navbar = () => {
  return (
    <nav className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-100 shadow-sm">
      <div className="mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-15">
          {/* Logo Section */}
          <div className="flex items-center space-x-3 flex-shrink-0">
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="relative">
                <Image
                  src="/logo.png"
                  alt="Kraken Logo"
                  height={25}
                  width={25}
                  className="transition-transform duration-200 group-hover:scale-105"
                />
              </div>
              <span className="text-xl font-semibold text-gray-900 tracking-tight">
                Kraken
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            <NavLink href="/">Marketplace</NavLink>
            <NavLink href="/" active>
              My Websites
            </NavLink>
            <NavLink href="/">My Orders</NavLink>
            <NavLink href="/">My Projects</NavLink>
            <NavLink href="/">Received Orders</NavLink>
          </div>

          {/* Right Section - Actions */}
          <div className="flex items-center space-x-2">
            {/* Desktop Actions */}
            <div className="hidden sm:flex items-center space-x-2">
              <ActionButton icon={PiWalletLight} label="Wallet" />
              <ActionButton icon={PiBellLight} label="Notifications" />
              <ActionButton icon={PiGearLight} label="Settings" />
              <ActionButton icon={PiUserCircleLight} label="Profile" />
            </div>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200"
              aria-label="Open menu"
            >
              <PiListLight className="w-6 h-6" />
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
};

// Navigation Link Component
interface NavLinkProps {
  href: string;
  children: React.ReactNode;
  active?: boolean;
}

const NavLink: React.FC<NavLinkProps> = ({
  href,
  children,
  active = false,
}) => {
  return (
    <Link
      href={href}
      className={`
        relative px-6 py-2 text-base font-semibold rounded-lg transition-all duration-200
        ${active ? "text-[#613fdd] " : "text-black hover:text-gray-900"}
        
      `}
    >
      {children}
      {active && (
        <span className="absolute -bottom-3 left-3 right-3 h-0.5 bg-[#613FDD] rounded-full" />
      )}
    </Link>
  );
};

// Action Button Component
interface ActionButtonProps {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  badge?: string;
  variant?: "default" | "primary";
}

const ActionButton: React.FC<ActionButtonProps> = ({
  icon: Icon,
  label,
  badge,
  variant = "default",
}) => {
  return (
    <button
      className={`
        relative p-2 rounded-lg transition-all duration-200 focus:outline-none 
        ${
          variant === "primary"
            ? "text-blue-600 hover:text-blue-700 "
            : "text-gray-600 hover:text-gray-900 "
        }
      `}
      aria-label={label}
      title={label}
    >
      <Icon className="w-5 h-5" />
      {badge && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
          {badge}
        </span>
      )}
    </button>
  );
};

export default Navbar;
